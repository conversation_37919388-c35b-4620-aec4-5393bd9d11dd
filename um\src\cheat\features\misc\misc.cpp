#include "pch.h"
#include "misc.hpp"
#include <string>
#include "../../offsets.hpp"
#include "../../../ItemIndex.hpp"
#include "../../gamedata.hpp" // GameData convenience wrapper system

void Misc::RenderMisc( const Reader& reader ) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  ImGui::GetStyle().AntiAliasedLines = false;
  ImGui::GetStyle().AntiAliasedFill = false;
  ImGui::GetStyle().AntiAliasedLinesUseTex = false;

  if (globals::Sound::enabled) {
    Sound();
  }

  if (globals::Hitmarker::enabled) {
    Hitmarker();
  }
  if (globals::Misc::RecoilCrosshair::enabled) {
    RecoilCrosshair();
  }
  if (globals::Crosshair::enabled) {
    SniperCrosshair();
  }
  if (globals::Watermark::enabled) {
    Watermark();
  }
}

void Misc::RecoilCrosshair() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // CLEAN: Use GameData convenience wrapper system
  Vector currentViewAngles = GameData::getViewAngles();
  uintptr_t localPlayerPawn = GameData::getLocalPlayerPawn();
  if (!localPlayerPawn) return;

  int numShots = GameData::getShotsFired(localPlayerPawn);

  // Only draw if player is shooting more than 1 bullet
  if (numShots <= 1) return;

  // Get aim punch angle for recoil compensation (this offset might need to be added to GameData in the future)
  const auto* gameVars = GameVars::getInstance();
  Vector aimPunchAngle = driver::read_memory<Vector>(gameVars->getDriver(), localPlayerPawn + Offset::Pawn::aimPunchAngle);

  // Calculate recoil-adjusted view angles
  Vector recoilAdjustedViewAngles = currentViewAngles;
  recoilAdjustedViewAngles.x += aimPunchAngle.x * 2.f;
  recoilAdjustedViewAngles.y += aimPunchAngle.y * 2.f;

  // Normalize angles
  Vector::Normalize(recoilAdjustedViewAngles);

  // Convert angles to forward vector
  Vector forwardVec;
  Vector::Angle(recoilAdjustedViewAngles, forwardVec);

  // Scale forward vector for projection
  forwardVec.x *= 10000.f;
  forwardVec.y *= 10000.f;
  forwardVec.z *= 10000.f;

  // CLEAN: Use GameData for player position
  Vector pawnOrigin = GameData::getPlayerPosition(localPlayerPawn);
  // Note: 0xCB0 offset might need to be added to GameData in the future
  Vector viewOffset = driver::read_memory<Vector>(gameVars->getDriver(), localPlayerPawn + 0xCB0);

  // Calculate start position (eye position)
  Vector startPos = pawnOrigin;
  Vector::Add(startPos, viewOffset);

  // Calculate end position
  Vector endPos = startPos;
  Vector::Add(endPos, forwardVec);

  // Convert to screen coordinates using GameData
  Vector endScreen;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, endPos, endScreen)) {
    // Draw the recoil crosshair dot
    Render::Dot(endScreen.x, endScreen.y, globals::Misc::RecoilCrosshair::size, globals::Misc::RecoilCrosshair::Color);
  }
}

void Misc::SniperCrosshair() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // CLEAN: Use GameData convenience wrapper system
  uintptr_t pawn = GameData::getLocalPlayerPawn();
  if (!pawn) return;

  bool scoped = GameData::getIsScoped(pawn);
  if (scoped) return;

  // Read weapon and item index using GameData
  uintptr_t weapon = GameData::getActiveWeapon(pawn);
  uint16_t idx = GameData::getWeaponItemIndex(weapon);

  // Only specific snipers
  if( idx != ItemIndex::WeaponAwp && idx != ItemIndex::WeaponSsg08 &&
      idx != ItemIndex::WeaponG3sg1 && idx != ItemIndex::WeaponScar20
  ) return;

  // Crosshair settings
  float gap       = globals::Crosshair::gap;
  float length    = globals::Crosshair::length;
  float thickness = globals::Crosshair::thickness;
  ImVec4 color     = globals::Crosshair::Color;

  // Determine pixel-aligned center based on thickness parity
  bool oddThickness = (static_cast<int>(thickness) % 2 != 0);
  float cx = globals::Screen::width  * 0.5f;
  float cy = globals::Screen::height * 0.5f;

  if (oddThickness) {
    cx = std::floor(cx) + 0.5f;
    cy = std::floor(cy) + 0.5f;
  } else {
    cx = std::round(cx);
    cy = std::round(cy);
  }

  // Calculate odd-thickness offsets
  float oddOffsetTop  = oddThickness ?  1.0f : 0.0f;   // hier: +1 px verschiebt obere Linie nach unten
  float oddOffsetLeft = oddThickness ?  1.0f : 0.0f;   // hier: +1 px verschiebt linke Linie nach rechts

  // Draw lines via filled rects for pixel perfection
  float halfThickness  = thickness * 0.5f;
  // Vertical top line
  Render::DrawRectFilled(
    cx - halfThickness,
    cy - gap - length + oddOffsetTop,    // hier angewendet: korrigiert 1 px nach unten
    thickness,
    length,
    color,
    0.0f
  );

  // Vertical bottom line
  Render::DrawRectFilled(
    cx - halfThickness,
    cy + gap,
    thickness,
    length,
    color,
    0.0f
  );

  // Horizontal left line
  Render::DrawRectFilled(
    cx - gap - length + oddOffsetLeft,   // hier angewendet: korrigiert 1 px nach rechts
    cy - halfThickness,
    length,
    thickness,
    color,
    0.0f
  );

  // Horizontal right line
  Render::DrawRectFilled(
    cx + gap,
    cy - halfThickness,
    length,
    thickness,
    color,
    0.0f
  );


  // Optional center dot
  if (globals::Crosshair::dotenabled) {
    float dotR = max(globals::Crosshair::dotSize, 0.5f);
    Render::fDot(cx, cy, dotR, color);
  }
}

void Misc::Hitmarker() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Damage tracking variables
  static int oldDamage = 0, oldNumRoundKills = 0, oldNumRoundHeadshotKills = 0;

  // CLEAN: Use GameData convenience wrapper system
  uintptr_t localPlayer = GameData::getLocalPlayerController();
  if (!localPlayer) return;

  // Get ActionTrackingServices and damage data
  const auto* gameVars = GameVars::getInstance();
  uintptr_t ActionTrackingServices = driver::read_memory<uintptr_t>(gameVars->getDriver(), localPlayer + Offset::PlayerController::m_pActionTrackingServices);
  if (!ActionTrackingServices) return;

  int DamageDealt = driver::read_memory<int>(gameVars->getDriver(), ActionTrackingServices + Offset::ActionTracking::TotalRoundDamageDealt);
  int NumRoundKills = driver::read_memory<int>(gameVars->getDriver(), ActionTrackingServices + Offset::ActionTracking::NumRoundKills);
  int NumRoundKillsHeadshots = driver::read_memory<int>(gameVars->getDriver(), ActionTrackingServices + Offset::ActionTracking::NumRoundKillsHeadshots);

  // Check for damage increase to trigger hitmarker
  if (DamageDealt > oldDamage) {
    if (globals::Hitmarker::enabled) {
      globals::flHitmarkerAlpha = 1.f;
    }
  }

  // Update old values
  oldNumRoundHeadshotKills = NumRoundKillsHeadshots;
  oldNumRoundKills = NumRoundKills;
  oldDamage = DamageDealt;

  // Hitmarker rendering logic
  static std::chrono::time_point<std::chrono::high_resolution_clock> hitmarkerStartTime;
  static bool hitmarkerActive = false;

  if (globals::flHitmarkerAlpha > 0 && !hitmarkerActive) {
    // Start the timer when hitmarker becomes visible
    hitmarkerStartTime = std::chrono::high_resolution_clock::now();
    hitmarkerActive = true;
  }

  if (globals::flHitmarkerAlpha <= 0)
    return;

  if (hitmarkerActive) {
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::high_resolution_clock::now() - hitmarkerStartTime);

    // Check if the hitmarker duration has elapsed
    if (elapsed.count() > globals::Hitmarker::duration) {
      globals::flHitmarkerAlpha = 0;
      hitmarkerActive = false;
      return;
    }
  }

  // Draw hitmarker lines
  Render::fLine(globals::centerX + globals::Hitmarker::gap, globals::centerY - globals::Hitmarker::gap,
    globals::centerX + globals::Hitmarker::gap + globals::Hitmarker::length, globals::centerY - globals::Hitmarker::gap - globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Top Right

  Render::fLine(globals::centerX + globals::Hitmarker::gap, globals::centerY + globals::Hitmarker::gap,
    globals::centerX + globals::Hitmarker::gap + globals::Hitmarker::length, globals::centerY + globals::Hitmarker::gap + globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Bottom Right

  Render::fLine(globals::centerX - globals::Hitmarker::gap, globals::centerY - globals::Hitmarker::gap,
    globals::centerX - globals::Hitmarker::gap - globals::Hitmarker::length, globals::centerY - globals::Hitmarker::gap - globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Top Left

  Render::fLine(globals::centerX - globals::Hitmarker::gap, globals::centerY + globals::Hitmarker::gap,
    globals::centerX - globals::Hitmarker::gap - globals::Hitmarker::length, globals::centerY + globals::Hitmarker::gap + globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Bottom Left

  // Gradually reduce alpha
  if (globals::flHitmarkerAlpha < 0)
    globals::flHitmarkerAlpha = 0;
}

void Misc::Sound() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Damage tracking variables
  static int oldDamage = 0, oldNumRoundKills = 0, oldNumRoundHeadshotKills = 0;

  // CLEAN: Use GameData convenience wrapper system
  uintptr_t localPlayer = GameData::getLocalPlayerController();
  if (!localPlayer) return;

  // Get ActionTrackingServices and damage data
  const auto* gameVars = GameVars::getInstance();
  uintptr_t ActionTrackingServices = driver::read_memory<uintptr_t>(gameVars->getDriver(), localPlayer + Offset::PlayerController::m_pActionTrackingServices);
  if (!ActionTrackingServices) return;

  int DamageDealt = driver::read_memory<int>(gameVars->getDriver(), ActionTrackingServices + Offset::ActionTracking::TotalRoundDamageDealt);
  int NumRoundKills = driver::read_memory<int>(gameVars->getDriver(), ActionTrackingServices + Offset::ActionTracking::NumRoundKills);
  int NumRoundKillsHeadshots = driver::read_memory<int>(gameVars->getDriver(), ActionTrackingServices + Offset::ActionTracking::NumRoundKillsHeadshots);

  // Sound path setup
  std::string soundPath;
  char documentsPath[MAX_PATH];
  if (SHGetFolderPathA(NULL, CSIDL_MYDOCUMENTS, NULL, SHGFP_TYPE_CURRENT, documentsPath) == S_OK) {
    soundPath = std::string(documentsPath) + "\\Nebula\\sound\\";
  } else {
    soundPath = ".\\sound\\"; // Fallback
  }

  std::string fullPathHit = soundPath + globals::Hitsound::sound;
  std::string fullPathKill = soundPath + globals::Killsound::sound;
  std::string fullPathKillHeadshot = soundPath + globals::HsKillsound::sound;

  // Volume control variables (static to persist between calls)
  static DWORD originalVolume = 0;
  static bool volumeInitialized = false;
  static auto restoreTime = std::chrono::steady_clock::now();
  static bool needsRestore = false;

  // Initialize original volume once
  if (!volumeInitialized) {
    if (waveOutGetVolume(0, &originalVolume) == MMSYSERR_NOERROR) {
      volumeInitialized = true;
    }
  }

  // Check if we need to restore volume (frame-based restoration)
  auto now = std::chrono::steady_clock::now();
  if (needsRestore && now >= restoreTime && volumeInitialized) {
    waveOutSetVolume(0, originalVolume);
    needsRestore = false;
  }

  // Safe sound playing with volume control
  auto playSound = [&](bool soundEnabled, const std::string& path) -> bool {
    namespace fs = std::filesystem;
    if (soundEnabled && fs::exists(path.c_str()) && globals::Sound::volume > 0.0f && volumeInitialized) {

      // Calculate new volume (0-100 to 0-65535)
      DWORD newVolume = static_cast<DWORD>((globals::Sound::volume / 100.0f) * 65535.0f);
      DWORD stereoVolume = (newVolume << 16) | newVolume;

      // Set volume temporarily
      waveOutSetVolume(0, stereoVolume);

      // Play sound
      BOOL result = sndPlaySoundA(path.c_str(), SND_FILENAME | SND_ASYNC);

      // Schedule volume restoration
      restoreTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(100);
      needsRestore = true;

      return result != FALSE;
    }
    return false;
  };

  // Check for damage increase to trigger sounds
  if (DamageDealt > oldDamage) {
    if (globals::Sound::enabled) {
      if (NumRoundKillsHeadshots > oldNumRoundHeadshotKills) {
        if (!playSound(globals::HsKillsound::enabled, fullPathKillHeadshot))
          if (!playSound(globals::Killsound::enabled, fullPathKill))
            playSound(globals::Hitsound::enabled, fullPathHit);
      }
      else if (NumRoundKills > oldNumRoundKills) {
        if (!playSound(globals::Killsound::enabled, fullPathKill))
          playSound(globals::Hitsound::enabled, fullPathHit);
      }
      else {
        playSound(globals::Hitsound::enabled, fullPathHit);
      }
    }
  }

  // Update old values
  oldNumRoundHeadshotKills = NumRoundKillsHeadshots;
  oldNumRoundKills = NumRoundKills;
  oldDamage = DamageDealt;
}

void Misc::Keystrokes(bool menuIsOpen) {
  // Set initial position on first frame
  if (globals::isFirstFrameKeystrokes) {
    globals::isFirstFrameKeystrokes = false;
  }

  // Calculate dimensions with scaling (using natural text spacing)
  float scale = globals::Keystrokes::scale;

  // Use the large FreeType font for calculations
  ImFont* keyFont = font; // 40f FreeType font
  float fontSize = 40.0f * scale;

  // Calculate natural text sizes and spacing
  float wWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "W").x;
  float aWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "A").x;
  float sWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "S").x;
  float dWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "D").x;
  float spaceWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "SPACE").x;

  // Natural spacing between letters (like ImGui::SameLine())
  float letterSpacing = fontSize * 0.3f; // Natural spacing based on font size
  float lineHeight = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "W").y;
  float verticalSpacing = fontSize * 0.1f; // Much smaller vertical spacing

  // Colors
  ImVec4 normalColor = globals::Keystrokes::Color;
  ImVec4 pressedColor = globals::Keystrokes::pressedColor;

  // Get background draw list (NEVER fades!)
  ImDrawList* drawList = ImGui::GetBackgroundDrawList();

  // Base position
  ImVec2 basePos = ImVec2(globals::Keystrokes::posX, globals::Keystrokes::posY);

  // Handle dragging with visible drag point when menu is open
  static bool isDragging = false;
  static ImVec2 dragOffset;

  // Calculate total area for dragging (based on natural text layout)
  float asdRowWidth = aWidth + sWidth + dWidth + letterSpacing * 2; // A-S-D row width
  float totalWidth = (std::max)((std::max)(wWidth, asdRowWidth), spaceWidth); // Widest element
  float totalHeight = lineHeight * 3 + verticalSpacing * 2; // 3 rows with tight vertical spacing

  if (menuIsOpen) {
    ImVec2 mousePos = ImGui::GetMousePos();

    // Check if mouse is over keystrokes area
    bool mouseOverKeystrokes = mousePos.x >= basePos.x && mousePos.x <= basePos.x + totalWidth &&
                              mousePos.y >= basePos.y && mousePos.y <= basePos.y + totalHeight;

    // Start dragging if clicked on keystrokes area
    if (ImGui::IsMouseClicked(ImGuiMouseButton_Left) && mouseOverKeystrokes) {
      isDragging = true;
      dragOffset = ImVec2(mousePos.x - basePos.x, mousePos.y - basePos.y);
    }

    // Handle dragging
    if (isDragging) {
      if (ImGui::IsMouseDown(ImGuiMouseButton_Left)) {
        globals::Keystrokes::posX = mousePos.x - dragOffset.x;
        globals::Keystrokes::posY = mousePos.y - dragOffset.y;
        basePos = ImVec2(globals::Keystrokes::posX, globals::Keystrokes::posY);
      } else {
        isDragging = false;
      }
    }
  } else {
    // Menu is closed - stop dragging
    isDragging = false;
  }

  // Draw keys with natural text positioning (like original ImGui layout)

  // W key (top row, centered)
  bool wPressed = GetAsyncKeyState('W') & 0x8000;
  ImVec4 wColor = wPressed ? pressedColor : normalColor;
  ImVec2 wPos = ImVec2(basePos.x + (totalWidth - wWidth) * 0.5f, basePos.y);
  drawList->AddText(keyFont, fontSize, wPos, ImColor(wColor), "W");

  // A-S-D keys (middle row, naturally spaced)
  float asdStartX = basePos.x + (totalWidth - asdRowWidth) * 0.5f; // Center the A-S-D row
  float asdY = basePos.y + lineHeight + verticalSpacing;

  // A key
  bool aPressed = GetAsyncKeyState('A') & 0x8000;
  ImVec4 aColor = aPressed ? pressedColor : normalColor;
  ImVec2 aPos = ImVec2(asdStartX, asdY);
  drawList->AddText(keyFont, fontSize, aPos, ImColor(aColor), "A");

  // S key
  bool sPressed = GetAsyncKeyState('S') & 0x8000;
  ImVec4 sColor = sPressed ? pressedColor : normalColor;
  ImVec2 sPos = ImVec2(asdStartX + aWidth + letterSpacing, asdY);
  drawList->AddText(keyFont, fontSize, sPos, ImColor(sColor), "S");

  // D key
  bool dPressed = GetAsyncKeyState('D') & 0x8000;
  ImVec4 dColor = dPressed ? pressedColor : normalColor;
  ImVec2 dPos = ImVec2(asdStartX + aWidth + sWidth + letterSpacing * 2, asdY);
  drawList->AddText(keyFont, fontSize, dPos, ImColor(dColor), "D");

  // Space bar (bottom row, centered)
  bool spacePressed = GetAsyncKeyState(VK_SPACE) & 0x8000;
  ImVec4 spaceColor = spacePressed ? pressedColor : normalColor;

  float spaceY = basePos.y + (lineHeight + verticalSpacing) * 2; // Third row with tight spacing
  ImVec2 spacePos = ImVec2(basePos.x + (totalWidth - spaceWidth) * 0.5f, spaceY);

  // Draw space bar text with large FreeType font
  drawList->AddText(keyFont, fontSize, spacePos, ImColor(spaceColor), "SPACE");
}

void Misc::Watermark() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Get FPS
  static std::chrono::time_point<std::chrono::steady_clock> lastUpdate = std::chrono::steady_clock::now();
  auto now = std::chrono::steady_clock::now();
  static float currentFPS = 0.0f;

  if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count() >= 500) {
    lastUpdate = now;
    currentFPS = ImGui::GetIO().Framerate;
  }

  // Get memory information
  MEMORYSTATUSEX memInfo;
  memInfo.dwLength = sizeof(MEMORYSTATUSEX);
  GlobalMemoryStatusEx(&memInfo);

  DWORDLONG totalPhysMem = memInfo.ullTotalPhys;
  DWORDLONG physMemUsed = totalPhysMem - memInfo.ullAvailPhys;

  // Convert to GB
  float usedGB = physMemUsed / (1024.0f * 1024.0f * 1024.0f);
  float totalGB = totalPhysMem / (1024.0f * 1024.0f * 1024.0f);

  // Individual component fade animations
  static float nebulaAlpha = 1.0f;
  static float fpsAlpha = 1.0f;
  static float ramAlpha = 1.0f;
  static float betaAlpha = 1.0f;

  static bool lastNebulaState = globals::Watermark::showNebula;
  static bool lastFpsState = globals::Watermark::showFPS;
  static bool lastRamState = globals::Watermark::showRAM;
  static bool lastBetaState = globals::Watermark::showBETA;

  float fadeSpeed = 8.f; // Half speed for smoother animation
  float deltaTime = ImGui::GetIO().DeltaTime;

  // Handle individual component fade animations
  if (globals::Watermark::showNebula != lastNebulaState) {
    lastNebulaState = globals::Watermark::showNebula;
  }
  if (globals::Watermark::showFPS != lastFpsState) {
    lastFpsState = globals::Watermark::showFPS;
  }
  if (globals::Watermark::showRAM != lastRamState) {
    lastRamState = globals::Watermark::showRAM;
  }
  if (globals::Watermark::showBETA != lastBetaState) {
    lastBetaState = globals::Watermark::showBETA;
  }

  // Animate alphas
  nebulaAlpha += (globals::Watermark::showNebula ? 1.0f : 0.0f - nebulaAlpha) * fadeSpeed * deltaTime;
  fpsAlpha += (globals::Watermark::showFPS ? 1.0f : 0.0f - fpsAlpha) * fadeSpeed * deltaTime;
  ramAlpha += (globals::Watermark::showRAM ? 1.0f : 0.0f - ramAlpha) * fadeSpeed * deltaTime;
  betaAlpha += (globals::Watermark::showBETA ? 1.0f : 0.0f - betaAlpha) * fadeSpeed * deltaTime;

  // Clamp alphas
  if (nebulaAlpha < 0.0f) nebulaAlpha = 0.0f;
  if (nebulaAlpha > 1.0f) nebulaAlpha = 1.0f;
  if (fpsAlpha < 0.0f) fpsAlpha = 0.0f;
  if (fpsAlpha > 1.0f) fpsAlpha = 1.0f;
  if (ramAlpha < 0.0f) ramAlpha = 0.0f;
  if (ramAlpha > 1.0f) ramAlpha = 1.0f;
  if (betaAlpha < 0.0f) betaAlpha = 0.0f;
  if (betaAlpha > 1.0f) betaAlpha = 1.0f;

  // Build component strings
  char fpsText[32];
  snprintf(fpsText, sizeof(fpsText), "FPS: %.0f", currentFPS);
  char ramText[64];
  snprintf(ramText, sizeof(ramText), "RAM: %.1f/%.1f", usedGB, totalGB);

  // Calculate individual component sizes for smooth positioning
  ImFont* watermarkFont = espfont;
  float fontSize = 9.0f;

  ImVec2 nebulaSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "NEBULA");
  ImVec2 fpsSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, fpsText);
  ImVec2 ramSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, ramText);
  ImVec2 betaSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "BETA");
  ImVec2 sepSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, " | ");

  // Calculate total animated width including animated separators
  float totalWidth = 0.0f;

  // Add Nebula
  if (nebulaAlpha > 0.01f) {
    totalWidth += nebulaSize.x * nebulaAlpha;
  }

  // Add separator between Nebula and FPS (fades with both components)
  if (nebulaAlpha > 0.01f && fpsAlpha > 0.01f) {
    float sepAlpha = (nebulaAlpha < fpsAlpha) ? nebulaAlpha : fpsAlpha; // Use minimum alpha
    totalWidth += sepSize.x * sepAlpha;
  }

  // Add FPS
  if (fpsAlpha > 0.01f) {
    totalWidth += fpsSize.x * fpsAlpha;
  }

  // Add separator between FPS and RAM
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f) && ramAlpha > 0.01f) {
    float prevAlpha = (nebulaAlpha > fpsAlpha) ? nebulaAlpha : fpsAlpha; // Use maximum of previous components
    float sepAlpha = (prevAlpha < ramAlpha) ? prevAlpha : ramAlpha;
    totalWidth += sepSize.x * sepAlpha;
  }

  // Add RAM
  if (ramAlpha > 0.01f) {
    totalWidth += ramSize.x * ramAlpha;
  }

  // Add separator between RAM and BETA
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f || ramAlpha > 0.01f) && betaAlpha > 0.01f) {
    float prevAlpha = nebulaAlpha;
    if (fpsAlpha > prevAlpha) prevAlpha = fpsAlpha;
    if (ramAlpha > prevAlpha) prevAlpha = ramAlpha;
    float sepAlpha = (prevAlpha < betaAlpha) ? prevAlpha : betaAlpha;
    totalWidth += sepSize.x * sepAlpha;
  }

  // Add BETA
  if (betaAlpha > 0.01f) {
    totalWidth += betaSize.x * betaAlpha;
  }

  // If nothing is visible, don't render
  if (totalWidth <= 0.0f) {
    return;
  }

  // Position in top-right corner
  float screenWidth = static_cast<float>(globals::Screen::width);
  float posX = screenWidth - totalWidth - 5.0f;
  float posY = 3.0f;

  // Get background draw list
  ImDrawList* drawList = ImGui::GetBackgroundDrawList();

  // Draw each component individually with smooth animated separators
  float currentX = posX;

  // Draw Nebula
  if (nebulaAlpha > 0.01f) {
    ImVec4 nebulaColor = globals::Watermark::Color;
    nebulaColor.w *= nebulaAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(nebulaColor), "NEBULA");
    currentX += nebulaSize.x * nebulaAlpha;
  }

  // Draw separator between Nebula and FPS with smooth fade
  if (nebulaAlpha > 0.01f && fpsAlpha > 0.01f) {
    float sepAlpha = (nebulaAlpha < fpsAlpha) ? nebulaAlpha : fpsAlpha;
    ImVec4 sepColor = globals::Watermark::Color;
    sepColor.w *= sepAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(sepColor), " | ");
    currentX += sepSize.x * sepAlpha;
  }

  // Draw FPS
  if (fpsAlpha > 0.01f) {
    ImVec4 fpsColor = globals::Watermark::Color;
    fpsColor.w *= fpsAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(fpsColor), fpsText);
    currentX += fpsSize.x * fpsAlpha;
  }

  // Draw separator between FPS and RAM with smooth fade
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f) && ramAlpha > 0.01f) {
    float prevAlpha = (nebulaAlpha > fpsAlpha) ? nebulaAlpha : fpsAlpha;
    float sepAlpha = (prevAlpha < ramAlpha) ? prevAlpha : ramAlpha;
    ImVec4 sepColor = globals::Watermark::Color;
    sepColor.w *= sepAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(sepColor), " | ");
    currentX += sepSize.x * sepAlpha;
  }

  // Draw RAM
  if (ramAlpha > 0.01f) {
    ImVec4 ramColor = globals::Watermark::Color;
    ramColor.w *= ramAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(ramColor), ramText);
    currentX += ramSize.x * ramAlpha;
  }

  // Draw separator between RAM and BETA with smooth fade
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f || ramAlpha > 0.01f) && betaAlpha > 0.01f) {
    float prevAlpha = nebulaAlpha;
    if (fpsAlpha > prevAlpha) prevAlpha = fpsAlpha;
    if (ramAlpha > prevAlpha) prevAlpha = ramAlpha;
    float sepAlpha = (prevAlpha < betaAlpha) ? prevAlpha : betaAlpha;
    ImVec4 sepColor = globals::Watermark::Color;
    sepColor.w *= sepAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(sepColor), " | ");
    currentX += sepSize.x * sepAlpha;
  }

  // Draw BETA
  if (betaAlpha > 0.01f) {
    ImVec4 betaColor = globals::Watermark::Color;
    betaColor.w *= betaAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(betaColor), "BETA");
  }

  // Draw gradient line perfectly aligned with animated text bounds
  float lineY = posY + nebulaSize.y + 2.0f; // 2px below text (using any component height)
  float lineStartX = posX; // Start exactly where text starts
  float lineEndX = posX + totalWidth; // End exactly where text ends
  float lineWidth = totalWidth; // Total animated width of the line

  // Draw gradient line from text start to text end with center-to-edges fade
  int segments = 50; // More segments for smoother gradient
  float centerX = posX + (totalWidth * 0.5f);
  float halfWidth = totalWidth * 0.5f;

  for (int i = 0; i < segments; i++) {
    float t = static_cast<float>(i) / (segments - 1); // 0 to 1

    // Calculate position from left edge to right edge
    float currentX = lineStartX + (t * lineWidth);
    float nextX = lineStartX + ((t + 1.0f/(segments-1)) * lineWidth);

    // Calculate distance from center (0 = center, 1 = edge)
    float distanceFromCenter = abs(currentX - centerX) / halfWidth;

    // Create fade from center to edges
    float alpha = globals::Watermark::gradientColor.w * (1.0f - distanceFromCenter);

    ImVec4 lineColor = ImVec4(globals::Watermark::gradientColor.x, globals::Watermark::gradientColor.y,
                              globals::Watermark::gradientColor.z, alpha);

    // Only draw if we're not at the last segment
    if (i < segments - 1) {
      drawList->AddLine(ImVec2(currentX, lineY), ImVec2(nextX, lineY), ImColor(lineColor), 1.0f);
    }
  }
}

void Misc::Spectators(bool menuIsOpen) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Get spectator list
  auto spectatorList = SpectatorData::getSpectatorListCopy();

  // Use same font as watermark (FreeType) with integer font sizes
  ImFont* spectatorFont = espfont;
  int baseFontSize = 9; // Base size in whole numbers
  float scaledSize = baseFontSize * globals::Spectators::scale;
  int fontSize = (int)(scaledSize + 0.5f); // Round to nearest integer
  if (fontSize < 8) fontSize = 8; // Minimum size

  // Individual spectator name animations (like watermark components)
  static std::vector<float> nameAlphas;
  static float titleAlpha = 1.0f;
  static float lineAlpha = 1.0f;

  static std::vector<std::string> lastSpectatorList;
  static bool lastHadSpectators = false;

  float fadeSpeed = 8.0f; // Same speed as watermark
  float deltaTime = ImGui::GetIO().DeltaTime;

  // Handle spectator list changes
  bool hasSpectators = !spectatorList.empty();
  bool listChanged = (spectatorList != lastSpectatorList);

  if (listChanged) {
    lastSpectatorList = spectatorList;
    nameAlphas.resize(spectatorList.size(), hasSpectators ? 0.0f : 1.0f);
  }

  if (hasSpectators != lastHadSpectators) {
    lastHadSpectators = hasSpectators;
  }

  // Animate title and line
  titleAlpha += (hasSpectators ? 1.0f : 0.0f - titleAlpha) * fadeSpeed * deltaTime;
  lineAlpha += (hasSpectators ? 1.0f : 0.0f - lineAlpha) * fadeSpeed * deltaTime;

  // Animate individual spectator names
  nameAlphas.resize(spectatorList.size());
  for (size_t i = 0; i < spectatorList.size(); i++) {
    nameAlphas[i] += (1.0f - nameAlphas[i]) * fadeSpeed * deltaTime;
    if (nameAlphas[i] > 1.0f) nameAlphas[i] = 1.0f;
  }

  // Clamp alphas
  if (titleAlpha < 0.0f) titleAlpha = 0.0f;
  if (titleAlpha > 1.0f) titleAlpha = 1.0f;
  if (lineAlpha < 0.0f) lineAlpha = 0.0f;
  if (lineAlpha > 1.0f) lineAlpha = 1.0f;

  // Check if anything is visible
  bool anyVisible = titleAlpha > 0.01f || lineAlpha > 0.01f;
  for (float alpha : nameAlphas) {
    if (alpha > 0.01f) {
      anyVisible = true;
      break;
    }
  }

  if (!anyVisible) {
    return;
  }

  // Create title text (no parentheses)
  std::string title = "SPECTATORS";

  // Calculate component sizes
  ImVec2 titleSize = spectatorFont->CalcTextSizeA((float)fontSize, FLT_MAX, 0.0f, title.c_str());

  std::vector<ImVec2> nameSizes;
  nameSizes.reserve(spectatorList.size());
  float maxNameWidth = titleSize.x; // Start with title width

  for (const auto& spectatorName : spectatorList) {
    ImVec2 nameSize = spectatorFont->CalcTextSizeA((float)fontSize, FLT_MAX, 0.0f, spectatorName.c_str());
    nameSizes.push_back(nameSize);
    if (nameSize.x > maxNameWidth) {
      maxNameWidth = nameSize.x;
    }
  }

  // Calculate total animated width (like watermark)
  float totalWidth = maxNameWidth;

  // Calculate total height for dragging
  float lineSpacing = 2.0f * globals::Spectators::scale;
  float totalHeight = titleSize.y + 5.0f; // Title + gap
  for (const auto& nameSize : nameSizes) {
    totalHeight += nameSize.y + lineSpacing;
  }

  // Position
  ImVec2 basePos = ImVec2(globals::Spectators::posX, globals::Spectators::posY);

  // Handle dragging (same as keystrokes)
  static bool isDragging = false;
  static ImVec2 dragOffset;

  if (menuIsOpen) {
    ImVec2 mousePos = ImGui::GetMousePos();

    // Check if mouse is over spectators area
    bool mouseOverSpectators = mousePos.x >= basePos.x && mousePos.x <= basePos.x + totalWidth &&
                              mousePos.y >= basePos.y && mousePos.y <= basePos.y + totalHeight;

    // Start dragging if clicked on spectators area
    if (ImGui::IsMouseClicked(ImGuiMouseButton_Left) && mouseOverSpectators) {
      isDragging = true;
      dragOffset = ImVec2(mousePos.x - basePos.x, mousePos.y - basePos.y);
    }

    // Handle dragging
    if (isDragging) {
      if (ImGui::IsMouseDown(ImGuiMouseButton_Left)) {
        globals::Spectators::posX = mousePos.x - dragOffset.x;
        globals::Spectators::posY = mousePos.y - dragOffset.y;
        basePos = ImVec2(globals::Spectators::posX, globals::Spectators::posY);
      } else {
        isDragging = false;
      }
    }
  } else {
    // Menu is closed - stop dragging
    isDragging = false;
  }

  // Get background draw list
  ImDrawList* drawList = ImGui::GetBackgroundDrawList();

  // Draw title with smooth animation (same as watermark)
  if (titleAlpha > 0.01f) {
    // Center the title horizontally
    float titleX = basePos.x + (totalWidth - titleSize.x) * 0.5f;

    ImVec4 titleColor = globals::Spectators::Color;
    titleColor.w *= titleAlpha;
    drawList->AddText(spectatorFont, (float)fontSize, ImVec2(titleX, basePos.y), ImColor(titleColor), title.c_str());
  }

  // Draw gradient line under title (same style as watermark)
  if (lineAlpha > 0.01f) {
    float lineY = basePos.y + titleSize.y + 1.0f;
    float titleX = basePos.x + (totalWidth - titleSize.x) * 0.5f;
    float lineStartX = titleX;
    float lineWidth = titleSize.x;

    // Draw gradient line with center-to-edges fade (same as watermark)
    int segments = 30;
    float centerX = titleX + (titleSize.x * 0.5f);
    float halfWidth = titleSize.x * 0.5f;

    for (int i = 0; i < segments; i++) {
      float t = static_cast<float>(i) / (segments - 1);

      float currentX = lineStartX + (t * lineWidth);
      float nextX = lineStartX + ((t + 1.0f/(segments-1)) * lineWidth);

      float distanceFromCenter = abs(currentX - centerX) / halfWidth;
      float alpha = globals::Spectators::Color.w * lineAlpha * (1.0f - distanceFromCenter);

      ImVec4 lineColor = ImVec4(globals::Spectators::Color.x, globals::Spectators::Color.y,
                                globals::Spectators::Color.z, alpha);

      if (i < segments - 1) {
        drawList->AddLine(ImVec2(currentX, lineY), ImVec2(nextX, lineY), ImColor(lineColor), 1.0f);
      }
    }
  }

  // Draw spectator names with watermark-style animation (individual fading + position animation)
  float currentY = basePos.y + titleSize.y + 5.0f; // Start position after title and line
  float nameLineSpacing = 2.0f * globals::Spectators::scale;

  for (size_t i = 0; i < spectatorList.size(); i++) {
    if (i >= nameAlphas.size()) break;

    float nameAlpha = nameAlphas[i];
    if (nameAlpha > 0.01f) {
      const auto& spectatorName = spectatorList[i];
      const auto& nameSize = nameSizes[i];

      // Center each name horizontally (like title)
      float nameX = basePos.x + (totalWidth - nameSize.x) * 0.5f;

      // Watermark-style animation: fade + position slide
      // Names slide from right to left as they appear (like watermark components)
      float animatedX = nameX + (1.0f - nameAlpha) * 20.0f; // Slide in from right

      // Vertical position animation: first name goes down, others go up
      float animatedY = currentY;
      if (i == 0) {
        // First spectator slides down
        animatedY = currentY - (1.0f - nameAlpha) * 10.0f;
      } else {
        // Other spectators slide up
        animatedY = currentY + (1.0f - nameAlpha) * 10.0f;
      }

      ImVec4 nameColor = globals::Spectators::Color;
      nameColor.w *= nameAlpha * 0.9f; // Slightly dimmer than title

      drawList->AddText(spectatorFont, (float)fontSize, ImVec2(animatedX, animatedY), ImColor(nameColor), spectatorName.c_str());
    }

    currentY += nameSizes[i].y + nameLineSpacing;
  }
}