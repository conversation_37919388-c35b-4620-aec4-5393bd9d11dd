#pragma once
#include "../../../driver/driver.hpp"
#include "../../../math/vector.hpp" // <PERSON><PERSON><PERSON><PERSON>, dass view_matrix_t hier oder in driver.hpp definiert ist
#include "../../entity.hpp"
#include "../../gamevars.hpp"
#include "../../gamedata.hpp" // GameData convenience wrapper system
#include "../../../render/render.hpp"
#include "../../animations/esp/health_animations.hpp"
#include "../../animations/esp/armor_animations.hpp"
#include "../../animations/esp/death_animations.hpp"
#include "../../bones.hpp"
#include "../../globals.hpp"
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <chrono>

#define MAX_BONE_LENGTH 100.0f  // Maximum bone length for skeleton drawing
inline float fontSize = 9.0f;

// Global variables for current player data - cleaner approach without parameters
namespace CurrentPlayer {
  inline Vector screenHead;
  inline Vector screenFeet;
  inline float boxHeight;
  inline float boxHalfWidth;
  inline std::string playerName;
  inline uint32_t playerFlags;
  inline uint16_t weaponIndex;
  inline int health;
  inline int armor;
  inline int entityId;
  inline uint64_t boneArray;
  inline Vector viewAngles;
  inline Vector eyeWorldPos;
  inline Vector screenProjectilePos;
  inline std::string projectileName;
}

class VISUALS {
  // Allow DeathAnimations to access private drawing functions
  friend class DeathAnimations;

public:
  static void RenderESP( HANDLE driverHandle, const Reader& reader );

private:
  // Drawing functions now use global variables - cleaner approach
  static void DrawPlayerBox();
  static void DrawPlayerCorneredBox();
  static void DrawPlayerInfo();
  static void DrawPlayerSnapline();
  static void DrawPlayerHealth();
  static void DrawPlayerArmor();
  static void DrawPlayerFilledBox();

  static void DrawPlayerHealthBarReactive();
  static void DrawPlayerHealthBarSolid();

  static void DrawViewline();
  static void DrawPlayerSkeleton();
  static void DrawPlayerJoints();
  static void DrawPlayerHead();
  static void DrawProjectile();

  static void DrawAimbotCircle();

  static void Darkmode();
};
