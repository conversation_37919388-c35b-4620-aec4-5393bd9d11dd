#pragma once
#include <Windows.h>

namespace Offset {

  // buttons
  inline DWORD ForceJump;
  inline DWORD ForceCrouch;
  inline DWORD ForceForward;
  inline DWORD ForceLeft;
  inline DWORD ForceRight;

  // offset
  inline DWORD PlantedC4;
  inline DWORD EntityList;
  inline DWORD Matrix;
  inline DWORD ViewAngle;
  inline DWORD LocalPlayerController;  // cs2_dumper::offsets::client_dll::dwLocalPlayerController
  inline DWORD LocalPlayerPawn;
  inline DWORD GlobalVars;

  // client
  inline DWORD Dormant;
  inline DWORD vecAbsOrigin;

  namespace ActionTracking {
    inline DWORD perRoundStats;
    inline DWORD matchStats;
    inline DWORD NumRoundKills;
    inline DWORD NumRoundKillsHeadshots;
    inline DWORD TotalRoundDamageDealt;
  }  // namespace ActionTracking

  namespace Entity {
    // DWORD Health = Offset::Pawn.CurrentHealth;
    // DWORD TeamID = Offset::Pawn.iTeamNum;
    inline DWORD IsAlive;
    inline DWORD PlayerPawn;
    inline DWORD iszPlayerName;
    // outdated DWORD EnemySensor = 0x1440;
    inline DWORD GravityScale;
  }  // namespace Entity

  namespace Pawn {
    // Services
    inline DWORD MovementServices;   // CPlayer_MovementServices*
    inline DWORD WeaponServices;     // CPlayer_WeaponServices*
    inline DWORD BulletServices;     // CCSPlayer_BulletServices*
    inline DWORD CameraServices;     // CPlayer_CameraServices*
    inline DWORD ViewModelServices;  // CPlayer_ViewModelServices*
    inline DWORD pClippingWeapon;    // C_CSWeaponBase*

    // View Related
    inline DWORD ViewModel;  // CCSPlayer_ViewModelServices::m_hViewModel
    inline DWORD CrouchState;
    inline DWORD isScoped;
    inline DWORD angEyeAngles;
    inline DWORD m_vecViewOffset;
    inline DWORD vecLastClipCameraPos;
    inline DWORD DesiredFov;
    inline DWORD iFovStart;

    // Status
    inline DWORD isDefusing;
    inline DWORD TotalHit;
    inline DWORD Pos;            // C_BasePlayerPawn::m_vOldOrigin
    inline DWORD CurrentArmor;   // C_CSPlayerPawn::m_ArmorValue
    inline DWORD MaxHealth;      // C_BaseEntity::m_iMaxHealth
    inline DWORD CurrentHealth;  // C_BaseEntity::m_iHealth

    // Scene & Bones
    inline DWORD GameSceneNode;  // C_BaseEntity::m_pGameSceneNode
    inline DWORD BoneArray;      // cs2_dumper::schemas::client_dll::CGameSceneNode::m_vecOrigin

    // Combat Related
    inline DWORD iShotsFired;
    inline DWORD flFlashMaxAlpha;
    inline DWORD flFlashDuration;
    inline DWORD aimPunchAngle;  // C_CSPlayerPawn::m_aimPunchAngle
    inline DWORD aimPunchCache;

    // Identification & Team
    inline DWORD iIDEntIndex;
    inline DWORD iTeamNum;

    // Flags & States
    inline DWORD fFlags;
    inline DWORD entitySpottedState;
    inline DWORD bSpottedByMask;  // C_CSPlayerPawn::entitySpottedState + EntitySpottedState_t::bSpottedByMask
    inline DWORD SPOTTED;
    inline DWORD AbsVelocity;
    inline DWORD IsBuying;
  }  // namespace Pawn

  namespace GlobalVar {
    inline DWORD RealTime         = 0x00;
    inline DWORD FrameCount       = 0x04;
    inline DWORD MaxClients       = 0x10;
    inline DWORD IntervalPerTick  = 0x14;
    inline DWORD CurrentTime      = 0x2C;
    inline DWORD CurrentTime2     = 0x30;
    inline DWORD TickCount        = 0x40;
    inline DWORD IntervalPerTick2 = 0x44;
    inline DWORD CurrentNetchan   = 0x0048;
    inline DWORD CurrentMap       = 0x0180;
    inline DWORD CurrentMapName   = 0x0188;
  }  // namespace GlobalVar

  namespace PlayerController {
    inline DWORD m_pActionTrackingServices;
    inline DWORD m_hPawn;
    inline DWORD m_pObserverServices;
    inline DWORD m_hObserverTarget;
    inline DWORD m_hController;
    inline DWORD PawnArmor;
    inline DWORD HasDefuser;
    inline DWORD HasHelmet;
  }  // namespace PlayerController

  namespace EconEntity {
    inline DWORD AttributeManager;  // C_AttributeContainer
    inline DWORD FallbackPaintKit;
    inline DWORD FallbackSeed;
    inline DWORD FallbackWear;
    inline DWORD FallbackStatTrak;
    inline DWORD szCustomName;

    inline DWORD EntityQuality;  // EconItemView::m_iEntityQuality
    inline DWORD ItemIDHigh;     // EconItemView::m_iItemIDHigh
  }  // namespace EconEntity

  namespace WeaponBaseData {
    inline DWORD WeaponDataPTR = 0x368;
    inline DWORD szName;
    inline DWORD Clip1;    // C_BasePlayerWeapon::m_iClip1
    inline DWORD MaxClip;  // CBasePlayerWeaponVData::m_iMaxClip1
    inline DWORD CycleTime;
    inline DWORD Penetration;
    inline DWORD WeaponType;
    inline DWORD Inaccuracy;  // CCSWeaponBaseVData::m_flInaccuracyMove
    inline DWORD inReload;

    inline DWORD WeaponSize = 0x50;
    inline DWORD ActiveWeapon;
    inline DWORD Item;  // C_AttributeContainer::m_Item
    inline DWORD ItemDefinitionIndex;
    inline DWORD MeshGroupMask;  // CModelState::m_MeshGroupMask
  }  // namespace WeaponBaseData

  namespace C4 {
    inline DWORD bBeingDefused;  // bool
    inline DWORD flDefuseCountDown;
    inline DWORD nBombSite;
  }  // namespace C4

  namespace InGameMoneyServices {
    inline DWORD MoneyServices;
    inline DWORD Account;
    inline DWORD TotalCashSpent;
    inline DWORD CashSpentThisRound;
  }  // namespace InGameMoneyServices

  namespace SmokeGrenadeProjectile  // C_BaseCSGrenadeProjectile
  {
    inline DWORD     nSmokeEffectTickBegin;     // int32_t
    inline DWORD     bDidSmokeEffect;           // bool
    inline DWORD     nRandomSeed;               // int32_t
    inline DWORD     vSmokeColor;               // Vector
    inline DWORD     vSmokeDetonationPos;       // Vector
    inline DWORD     VoxelFrameData;            // CUtlVector<uint8_t>
    inline DWORD     bSmokeVolumeDataReceived;  // bool
    inline uintptr_t bSmokeEffectSpawned;       // bool
  }  // namespace SmokeGrenadeProjectile

}  // namespace Offset
