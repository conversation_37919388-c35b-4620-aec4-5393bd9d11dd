﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="imgui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_dx11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\misc\freetype\imgui_freetype.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="imgui\misc\cpp\imgui_stdlib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\intel_driver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\kdmapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\portable_executable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Linking\include\kdm\service.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\window\window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\math\vector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\entity.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\gamevars.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\driver\driver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\driver\driver_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\OffsetsUpdater.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\network.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\pch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\getmodulebase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\getprocessid.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\render\render.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\features\legitbot\legitbot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\features\visuals\visuals.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\window\overlayrender.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\json_utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\globals_vars.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\features\misc\misc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\core\animation_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\esp\armor_animations.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\esp\death_animations.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\esp\health_animations.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\animations\ui\menu_animations.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\config\config_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\cheat\gamedata.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="imgui\imconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_dx11.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="output\client.dll.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="output\offsets.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ProcessManager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="a2x\buttons.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="a2x\client.dll.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="a2x\offsets.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_extra.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\dlg\dlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\dlg\output.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\ftconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\ftheader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\ftmodule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\ftoption.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\ftstdlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\integer-types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\mac-support.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\config\public-macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svbdf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svcfftl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svcid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svfntfmt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svgldict.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svgxval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svkern.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svmetric.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svmm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svotval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svpfr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svpostnm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svprop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svpscmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svpsinfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svsfnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svttcmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svtteng.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svttglyf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\services\svwinfnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\autohint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\cffotypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\cfftypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\compiler-macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftcalc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftdebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftdrv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftgloadr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\fthash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftmemory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftmmtypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftobjs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftpsprop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftrfork.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftserv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftstream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\fttrace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\ftvalid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\psaux.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\pshints.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\sfnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\svginterface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\t1types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\tttypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\internal\wofftypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\freetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftadvanc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftbbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftbdf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftbitmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftbzip2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftcache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftchapters.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftcid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftcolor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftdriver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\fterrdef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\fterrors.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftfntfmt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftgasp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftglyph.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftgxval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftgzip.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftincrem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftlcdfil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftlogging.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftlzw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftmac.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftmm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftmodapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftmoderr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftotval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftoutln.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftparams.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftpfr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftrender.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftsizes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftsnames.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftstroke.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftsynth.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftsystem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\fttrigon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\fttypes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ftwinfnt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\otsvg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\t1tables.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\ttnameid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\tttables.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\freetype\tttags.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\ft2build.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\misc\freetype\imgui_freetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\misc\cpp\imgui_stdlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\misc\single_file\imgui_single_file.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_av1std.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_av1std_decode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h264std.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h264std_decode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h264std_encode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h265std.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h265std_decode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codec_h265std_encode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="imgui\vk_video\vulkan_video_codecs_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="a2x\client_dll.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\intel_driver.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\intel_driver_resource.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\kdmapper.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\nt.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\portable_executable.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\service.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\kdm\utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\curl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\curlver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\easy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\mprintf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\multi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\options.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\stdcheaders.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\system.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\typecheck-gcc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\urlapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\curl\websockets.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\zlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Linking\include\zconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\driver\HexDriver.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\window\window.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\math\vector.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\driver\driver.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\entity.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\gamevars.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\driver\driver_manager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\offsets.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\network.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\bones.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\OffsetsUpdater.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\pch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\getmodulebase.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\getprocessid.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\fonts\fonts.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\render\render.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\features\legitbot\legitbot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\ItemIndex.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\globals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\features\misc\misc.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\core\animation_manager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\core\animation_types.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\core\animation_utils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\esp\armor_animations.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\esp\death_animations.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\esp\health_animations.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\animations\ui\menu_animations.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\utils\nlohmann\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\config\config_manager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\config\nlohmann\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\features\visuals\visuals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\gamedata.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="src\cheat\features\mouse\Mouse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Library Include="Linking\lib\freetype\freetype.lib" />
    <Library Include="Linking\lib\freetype.lib" />
    <Library Include="Linking\lib\kdmapper_lib-Release.lib" />
    <Library Include="Linking\lib\libcurl.lib" />
  </ItemGroup>
</Project>